<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Only include config if $pdo is not already defined
if (!isset($pdo)) {
    require 'includes/config.php';
}

$limit = 12; 
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1; 
$offset = ($page - 1) * $limit; 

$search = isset($_GET['search']) ? $_GET['search'] : '';

if ($search) {
    $total_blogs_sql = "SELECT COUNT(*) FROM blog_posts WHERE title LIKE :search OR short_description LIKE :search";
    $sql = "SELECT * FROM blog_posts WHERE title LIKE :search OR short_description LIKE :search ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
} else {
    $total_blogs_sql = "SELECT COUNT(*) FROM blog_posts";
    $sql = "SELECT * FROM blog_posts ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
}

$stmt = $pdo->prepare($total_blogs_sql);
if ($search) {
    $search_term = '%' . $search . '%';
    $stmt->bindParam(':search', $search_term, PDO::PARAM_STR);
}
$stmt->execute();
$total_blogs = $stmt->fetchColumn();

$stmt = $pdo->prepare($sql);
if ($search) {
    $stmt->bindParam(':search', $search_term, PDO::PARAM_STR);
}
$stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
$stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$blogs = $stmt->fetchAll(PDO::FETCH_ASSOC);

$total_pages = ceil($total_blogs / $limit);
?>

<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Blog - Safe Car Hauler</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="SEO Description" />

    <link rel="canonical" href="https://safecarhauler.com/blog" />
    <link rel="stylesheet" type="text/css" href="public/css/index.css" />
    <link rel="stylesheet" type="text/css" href="public/css/styles.css" />
    <link rel="stylesheet" type="text/css" href="public/css/components.css" />
    <link rel="stylesheet" type="text/css" href="public/css/main.css" />
    <link rel="stylesheet" type="text/css" href="public/css/Blog.css" />
    <link rel="icon" type="image/x-icon" href="public/favicon.ico">
    <!-- iubenda start -->
  <script type="text/javascript">
        var _iub = _iub || {}; 
        _iub.cons_instructions = _iub.cons_instructions || []; 
        _iub.cons_instructions.push(["init", {api_key: "ri0L77KZSPdst3FYVu0GB0GG0oQ59GS7"}]);
    </script>
    <script type="text/javascript" src="https://cdn.iubenda.com/cons/iubenda_cons.js" async></script>
    <script type="text/javascript">
        var _iub = _iub || [];
        _iub.csConfiguration = {"siteId":3966585,"cookiePolicyId":28502363,"lang":"en","storage":{"useSiteId":true}};
    </script>
    <script type="text/javascript" src="https://cs.iubenda.com/autoblocking/3966585.js"></script>
    <script type="text/javascript" src="//cdn.iubenda.com/cs/gpp/stub.js"></script>
    <script type="text/javascript" src="//cdn.iubenda.com/cs/iubenda_cs.js" charset="UTF-8" async></script>

<!-- iubenda end -->
  </head>
  <body>
  <div class="home-p">
  <?php include 'includes/header.php' ?>
  <hr class="header-hr">

      <div class="column_one" style="margin-top: 50px;">
        <div class="container-xs">
          <div>
            <div class="columnourblog">
              <p class="ourblog ui text size-textlg">Our Blog</p>
              <h1 class="lateststories ui heading size-headingmd">Latest stories for you</h1>
              <form method="GET" action="" class="search">
                <input 
                    name="search" 
                    placeholder="Search" 
                    type="text" 
                    value="<?php echo htmlspecialchars($search); ?>" 
                    onkeyup="searchFunction(this.value)" 
                />
              </form>
                <?php if (!empty($blogs)): ?>
               <?php $blog = $blogs[0]; ?>
               <div class="rowhowtochange" style="background-image: url('uploads/<?php echo htmlspecialchars($blog['image']); ?>'); background-size: cover; background-repeat: no-repeat;">
               <a href="blog/<?php echo $blog['url']; ?>" style="text-decoration:none;">
               <div class="columnhowtochan">
                <h2 class="howtochange ui heading size-text2xl">
                <?php echo htmlspecialchars($blog['title']); ?>
                </h2>
                <p class="movingtoanew text size-textmd">
                <?php echo htmlspecialchars($blog['short_description']); ?>
               </p>
               <p class="july162024 ui text size-textxs">
                <?php echo date('F d, Y', strtotime($blog['created_at'])); ?>
               </p>
                </div>
                </a>
               </div>
               
              <?php else: ?>
             <?php endif; ?>
            </div>
            <div class="blog-1">
            <?php if (!empty($blogs)): ?>
    <?php 
    $counter = 0;
    foreach (array_slice($blogs, 1) as $blog): ?>
        <div class="columnview">
            <div class="stackview">
                <img src="uploads/<?php echo htmlspecialchars($blog['image']); ?>" alt="Image" class="image-1" />
               <a href="blog/<?php echo $blog['url']; ?>" style="text-decoration:none;">
                    <div class="view"></div>
                </a>
            </div>
            <div class="columnhowtochan-1">
                <h3 class="howtochange-1 ui heading size-textxl"><?php echo htmlspecialchars($blog['title']); ?></h3>
                
                <p class="description ui text size-textmd"><?php echo htmlspecialchars($blog['short_description']); ?></p>
                <p class="ui text size-textxs"><?php echo date('F d, Y', strtotime($blog['created_at'])); ?></p>
            </div>
        </div>

        <?php 
        $counter++; 

        if ($counter == 5): ?>
            <div class="quote-block">
                <h2>Want an immediate quote for shipping your car?</h2>
                <p>Use our calculator below or give us a call at <a href="tel:+18778788008" style="color: #D2B67F;">(*************</a></p>
                <button onclick="window.location.href='index.php';">Get an instant quote</button>
            </div>
        <?php endif; ?>

    <?php endforeach; ?>
<?php else: ?>
    <p>No blog posts found.</p>
<?php endif; ?>
            </div>
            <div class="rowofcounter">
    <div class="rowone">
        <?php if ($page > 1): ?>
            <a href="?page=<?php echo $page - 1; ?>" class="flex-row-center-center one">Previous</a>
        <?php endif; ?>

        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
            <a href="?page=<?php echo $i; ?>" class="flex-row-center-center one-1"><?php echo $i; ?></a>
        <?php endfor; ?>

        <?php if ($page < $total_pages): ?>
            <a href="?page=<?php echo $page + 1; ?>" class="flex-row-center-center one">Next</a>
        <?php endif; ?>
        
        <!--<p class="ui text size-textmd">Page <?php echo $page; ?> of <?php echo $total_pages; ?></p>-->
    </div>
</div>

          </div>
        </div>
      </div></div>
      <?php include 'includes/footer.php'; ?>
      <script>
function searchFunction(query) {
    const url = new URL(window.location.href);
    url.searchParams.set('search', query);
    window.history.pushState({}, '', url); 
    fetchResults(query); 
}

function fetchResults(query) {
    fetch(`?search=${query}`)
        .then(response => response.text())
        .then(data => {
            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(data, 'text/html');
            const newResults = htmlDoc.querySelector('.blog-1').innerHTML;
            document.querySelector('.blog-1').innerHTML = newResults;
        });
}
</script>
    </div>
  </body>
</html>