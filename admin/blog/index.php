<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', '../error_log.txt');
include '../includes/config.php';




// Get the requested URL
$request = $_SERVER['REQUEST_URI'];
$slug = basename($request);

// If empty, show blog listing
if(empty($slug) || $slug == 'blog') {
    require '../blog.php';
    exit;
}

// Otherwise show the blog post
require '../blog_details.php';

?>

<html lang="en">
<head>
    <meta charset="utf-8" />
    <title>Blog - Safe Car Hauler</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- შეცვალეთ ყველა CSS ფაილის მისამართი -->
   <link rel="stylesheet" type="text/css" href="../../public/css/index.css" />
<link rel="stylesheet" type="text/css" href="../../public/css/styles.css" />
<link rel="stylesheet" type="text/css" href="../../public/css/components.css" />
<link rel="stylesheet" type="text/css" href="../../public/css/main.css" />
<link rel="stylesheet" type="text/css" href="../../public/css/BlogDeitals.css" />
<link rel="icon" type="image/x-icon" href="../../public/favicon.ico">
    
    <base href="https://safecarhauler.com/" />
    <!-- iubenda სკრიპტები -->
</head>
<body>
    <?php include '../includes/header.php' ?>
    
    <!-- თქვენი კონტენტი -->
    <?php include 'includes/footer.php'; ?>
    
    
    <!-- შეცვალეთ JS ფაილის მისამართი -->
    <script src="../../public/js/send.js"></script>
</body>
</html>