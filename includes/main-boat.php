<?php
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

require '../vendor/autoload.php';
include 'includes/config.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $transport_from = $_POST['transport_from'];
    $transport_to = $_POST['transport_to'];
    $transport_type = $_POST['transport_type'];
    $vehicle_year = $_POST['vehicle_year'];
    $vehicle_brand = $_POST['vehicle_brand'];
    $vehicle_model = $_POST['vehicle_model'];
    $email = $_POST['email'];
    $available_date = $_POST['available_date'];
    $name = $_POST['name'];
    $phone = $_POST['phone'];

    $required_fields = [
        'transport_from', 'transport_to', 'transport_type', 'vehicle_year',
        'vehicle_brand', 'vehicle_model', 'email',
        'available_date', 'name', 'phone'
    ];

    $missing_fields = [];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $field;
        }
    }

    if (!empty($missing_fields)) {
        echo '<div class="error-message">All fields are required!</div>';
        return; 
    }

    $sql = "INSERT INTO orders 
            (transport_from, transport_to, transport_type, vehicle_year, vehicle_brand, vehicle_model, email, available_date, name, phone)
            VALUES (:transport_from, :transport_to, :transport_type, :vehicle_year, :vehicle_brand, :vehicle_model, :email, :available_date, :name, :phone)";

    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':transport_from' => $transport_from,
            ':transport_to' => $transport_to,
            ':transport_type' => $transport_type,
            ':vehicle_year' => $vehicle_year,
            ':vehicle_brand' => $vehicle_brand,
            ':vehicle_model' => $vehicle_model,
            ':email' => $email,
            ':available_date' => $available_date,
            ':name' => $name,
            ':phone' => $phone
        ]);
    } catch (PDOException $e) {
        echo '<div class="error-message">Error inserting data: ' . $e->getMessage() . '</div>';
        exit();
    }

    $mail = new PHPMailer(true);
    try {
        $mail->isSMTP();
        $mail->Host = 'smtp.gmail.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>'; 
        $mail->Password = 'qged hvyy zboe wxpt';
        $mail->SMTPSecure = 'tls';
        $mail->Port = 587;
        $mail->CharSet = 'UTF-8';

        $mail->setFrom('<EMAIL>', 'New Order');
        $mail->addAddress('<EMAIL>'); 

        $mail->isHTML(true);
        $mail->Subject = 'New Vehicle Transport Order';
        $mail->Body = "
        <div style='background-color: #1e1e1e; color: #ffffff; padding: 20px; font-family: Arial, sans-serif;'>
            <h2 style='text-align: center; color: #00ff6f;'>New Vehicle Transport Order</h2>
            <p style='font-size: 16px; color: #cccccc;'>You have received a new transport order. Here are the details:</p>
            <table style='width: 100%; border-collapse: collapse;'>
                <tr>
                    <td style='padding: 10px; border: 1px solid #444;'>Transport From:</td>
                    <td style='padding: 10px; border: 1px solid #444;'>$transport_from</td>
                </tr>
                <tr>
                    <td style='padding: 10px; border: 1px solid #444;'>Transport To:</td>
                    <td style='padding: 10px; border: 1px solid #444;'>$transport_to</td>
                </tr>
                <tr>
                    <td style='padding: 10px; border: 1px solid #444;'>Transport Type:</td>
                    <td style='padding: 10px; border: 1px solid #444;'>$transport_type</td>
                </tr>
                <tr>
                    <td style='padding: 10px; border: 1px solid #444;'>Vehicle Year:</td>
                    <td style='padding: 10px; border: 1px solid #444;'>$vehicle_year</td>
                </tr>
                <tr>
                    <td style='padding: 10px; border: 1px solid #444;'>Vehicle Brand:</td>
                    <td style='padding: 10px; border: 1px solid #444;'>$vehicle_brand</td>
                </tr>
                <tr>
                    <td style='padding: 10px; border: 1px solid #444;'>Vehicle Model:</td>
                    <td style='padding: 10px; border: 1px solid #444;'>$vehicle_model</td>
                </tr>
                <tr>
                    <td style='padding: 10px; border: 1px solid #444;'>Email:</td>
                    <td style='padding: 10px; border: 1px solid #444;'>$email</td>
                </tr>
                <tr>
                    <td style='padding: 10px; border: 1px solid #444;'>Available Date:</td>
                    <td style='padding: 10px; border: 1px solid #444;'>$available_date</td>
                </tr>
                <tr>
                    <td style='padding: 10px; border: 1px solid #444;'>Name:</td>
                    <td style='padding: 10px; border: 1px solid #444;'>$name</td>
                </tr>
                <tr>
                    <td style='padding: 10px; border: 1px solid #444;'>Phone:</td>
                    <td style='padding: 10px; border: 1px solid #444;'>$phone</td>
                </tr>
            </table>
            <p style='text-align: center; color: #00ff6f; font-size: 14px; padding-top: 20px;'>Thank you for using our service!</p>
        </div>
        ";

        $mail->send();
        
        // წარმატებული გაგზავნის შემდეგ გადამისამართება
        header("Location: thank-you.php");
        exit();
    } catch (Exception $e) {
        echo '<div class="error-message">Message could not be sent. Mailer Error: ' . $mail->ErrorInfo . '</div>';
    }
}
?>